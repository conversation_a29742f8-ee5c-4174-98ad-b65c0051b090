"use client";

import { usePathname, useRouter } from "next/navigation";
import { FaCalculator, FaCreativeCommonsZero, FaFileInvoiceDollar, FaRobot, FaTachometerAlt } from 'react-icons/fa';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";

// Menu items configuration
const sidebarItems = [
  {
    id: 0,
    title: 'Dashboard',
    icon: FaTachometerAlt,
    route: '/dashboard'
  },
  {
    id: 1,
    title: 'Sales Invoice Generator',
    icon: FaFileInvoiceDollar,
    route: '/sales-invoice-generator'
  },
  {
    id: 2,
    title: 'Invoice Automation',
    icon: FaRobot,
    route: '/invoice-automation'
  },
  {
    id: 3,
    title: 'Accounting & Tax Assistant',
    icon: FaCalculator,
    route: '/accounting-tax-assistant'
  },
  {
    id: 4,
    title: 'Xero Automation',
    icon: FaCreativeCommonsZero,
    route: '/xero'
  }
];

// Map routes to sidebar item IDs
const routeToItemId = {
  "/dashboard": 0,
  "/sales-invoice-generator": 1,
  "/invoice-automation": 2,
  "/accounting-tax-assistant": 3,
  "/xero": 4,
  "/profile": 0 // Profile uses dashboard as active item
};

// Function to get the active item ID from pathname
const getActiveItemId = (pathname) => {
  // Direct match
  if (routeToItemId[pathname] !== undefined) {
    return routeToItemId[pathname];
  }

  // Check if it's a sub-route
  if (pathname.startsWith('/xero/')) {
    return routeToItemId['/xero'];
  }

  // Default to dashboard
  return 0;
};

export function AppSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const activeItemId = getActiveItemId(pathname);

  const handleNavigation = (route) => {
    router.push(route);
  };

  return (
    <Sidebar collapsible="icon" className="border-r">
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center justify-center p-2">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-teal-500 flex items-center justify-center group-data-[collapsible=icon]:w-6 group-data-[collapsible=icon]:h-6">
              <span className="text-white font-bold text-lg group-data-[collapsible=icon]:text-sm">M</span>
            </div>
            <span className="font-bold text-xl text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-teal-600 group-data-[collapsible=icon]:hidden">
              MizuFlow
            </span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Applications</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeItemId === item.id;
                
                return (
                  <SidebarMenuItem key={item.id}>
                    <SidebarMenuButton
                      onClick={() => handleNavigation(item.route)}
                      isActive={isActive}
                      tooltip={item.title}
                      className="w-full"
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border">
        <div className="p-2 text-xs text-sidebar-foreground/70 text-center group-data-[collapsible=icon]:hidden">
          © 2024 MizuFlow
        </div>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
