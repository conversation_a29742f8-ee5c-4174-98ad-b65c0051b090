'use client';

import { useAuth } from '@/context/AuthContext';
import { yupResolver } from '@hookform/resolvers/yup';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { Button } from '../ui/button';
import FormInput from '../ui/FormInput';
import GoogleLoginButton from './GoogleLoginButton';

// Form validation schema
const schema = yup.object().shape({
  email: yup
    .string()
    .email('Please enter a valid email')
    .required('Email is required'),
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters'),
});

export default function LoginForm() {
  const { login, loading, error, clearError } = useAuth();
  const [serverErrors, setServerErrors] = useState({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError: setFormError,
  } = useForm({
    resolver: yupResolver(schema),
  });

  // Clear errors when component mounts and unmounts
  useEffect(() => {
    clearError();
    return () => clearError();
  }, [clearError]);

  // Process server errors and set them in the form
  useEffect(() => {
    if (error && typeof error === 'object') {
      setServerErrors(error);

      // Set field-specific errors in the form
      Object.keys(error).forEach(fieldName => {
        // Skip 'general' error as it's displayed separately
        if (fieldName !== 'general' && fieldName in schema.fields) {
          setFormError(fieldName, {
            type: 'server',
            message: error[fieldName]
          });
        }
      });
    } else {
      setServerErrors({});
    }
  }, [error, setFormError]);

  const onSubmit = async (data) => {
    try {
      setServerErrors({});
      await login(data.email, data.password);
    } catch (error) {
      // Error handling is done in AuthContext and the above useEffect
    }
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
        Log in to your account
      </h2>

      {serverErrors.general && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {serverErrors.general}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormInput
          name="email"
          label="Email Address"
          type="email"
          autoComplete="email"
          {...register('email')}
          error={errors.email || (serverErrors.email && { message: serverErrors.email })}
        />

        <FormInput
          name="password"
          label="Password"
          type="password"
          autoComplete="current-password"
          {...register('password')}
          error={errors.password || (serverErrors.password && { message: serverErrors.password })}
        />

        <div className="flex items-center justify-between">
          <div className="text-sm">
            <Link
              href="/forgot-password"
              className="text-blue-600 hover:text-blue-500"
              onClick={clearError}
            >
              Forgot your password?
            </Link>
          </div>
        </div>

        <Button
          type="submit"
          variant="default"
          size="default"
          className="w-full"
          disabled={loading}
        >
          {loading ? 'Logging in...' : 'Log in'}
        </Button>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        <GoogleLoginButton />

        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{' '}
            <Link
              href="/register"
              className="text-blue-600 hover:text-blue-500"
              onClick={clearError}
            >
              Sign up
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
}