'use client';

import { useAuth } from '@/context/AuthContext';
import { yupResolver } from '@hookform/resolvers/yup';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { Button } from '../ui/button';
import FormInput from '../ui/FormInput';
import GoogleLoginButton from './GoogleLoginButton';

// Form validation schema
const schema = yup.object().shape({
  email: yup
    .string()
    .email('Please enter a valid email')
    .required('Email is required'),
});

export default function RegisterForm() {
  const { register: registerUser, loading, error, clearError } = useAuth();
  const [registered, setRegistered] = useState(false);
  const [serverErrors, setServerErrors] = useState({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError: setFormError,
  } = useForm({
    resolver: yupResolver(schema),
  });

  // Clear errors when component mounts and unmounts
  useEffect(() => {
    clearError();
    return () => clearError();
  }, [clearError]);

  // Process server errors and set them in the form
  useEffect(() => {
    if (error && typeof error === 'object') {
      setServerErrors(error);

      // Set field-specific errors in the form
      Object.keys(error).forEach(fieldName => {
        // Skip 'general' error as it's displayed separately
        if (fieldName !== 'general' && fieldName in schema.fields) {
          setFormError(fieldName, {
            type: 'server',
            message: error[fieldName]
          });
        }
      });
    } else {
      setServerErrors({});
    }
  }, [error, setFormError]);

  const onSubmit = async (data) => {
    try {
      setServerErrors({});
      await registerUser(data.email);
      setRegistered(true);
    } catch (error) {
      // Error handling is done in AuthContext and the above useEffect
    }
  };

  if (registered) {
    return (
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
          Registration Successful!
        </h2>
        <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded text-center">
          <p>
            Please check your email for activation instructions.
          </p>
          <p className="mt-2">
            You will need to activate your account before you can log in.
          </p>
        </div>
        <div className="text-center">
          <Link
            href="/login"
            className="text-blue-600 hover:text-blue-500"
            onClick={clearError}
          >
            Back to login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
        Create an account
      </h2>

      {serverErrors.general && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {serverErrors.general}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormInput
          name="email"
          label="Email Address"
          type="email"
          autoComplete="email"
          {...register('email')}
          error={errors.email || (serverErrors.email && { message: serverErrors.email })}
        />

        <Button
          type="submit"
          variant="primary"
          size="md"
          className="w-full"
          isLoading={loading}
        >
          Register
        </Button>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        <GoogleLoginButton text="Sign up with Google" />

        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              href="/login"
              className="text-blue-600 hover:text-blue-500"
              onClick={clearError}
            >
              Log in
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
}